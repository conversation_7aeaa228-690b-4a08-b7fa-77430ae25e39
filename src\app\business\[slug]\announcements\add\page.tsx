"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { AppDispatch, RootState } from "@/store";
import { fetchAllStates, fetchAllLocations } from "@/store/slices/commonSlice";
import {
  fetchAnnouncementsCategory,
  fetchAnnouncementType,
  createAnnouncement
} from "@/store/slices/org-announcement/announcement";
import { useSnackbar } from "@/contexts/SnackbarContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Upload, Calendar, MapPin, Phone, Mail, Link, ArrowLeft } from "lucide-react";
import { BusinessRoutes } from "@/utils/businessRoutes";
import DateRangePicker from "@/components/common/DatePicker";
import { channelsList } from "@/utils/constantDropdown";

interface VideoLink {
  label: string;
  videoUrl: string;
}

interface AnnouncementFormData {
  title: string;
  fullContent: string;
  image: string | null;
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  area?: string;
  type: string;
  category: string;
  announcementFormat: string;
  registrationLink?: string;
  address?: string;
  state?: string;
  city?: string;
  zipcode?: string;
  firstName?: string;
  lastName?: string;
  contactPhone?: string;
  contactEmail?: string;
  quickLinks?: string[];
  videos?: VideoLink[];
  isPublished: boolean;
}

// Validation Schema
const validationSchema = yup.object().shape({
  title: yup
    .string()
    .required("Title is required")
    .min(3, "Title must be at least 3 characters")
    .max(100, "Title must not exceed 100 characters"),

  fullContent: yup
    .string()
    .required("Content is required")
    .min(10, "Content must be at least 10 characters")
    .max(500, "Content must not exceed 500 characters"),

  image: yup.mixed().nullable(),

  dateRange: yup.object().shape({
    start: yup.mixed().nullable(),
    end: yup.mixed().nullable()
  }).nullable(),

  area: yup.string().nullable(),

  type: yup
    .string()
    .required("Type is required"),

  category: yup
    .string()
    .required("Category is required"),

  announcementFormat: yup
    .string()
    .required("Announcement format is required"),

  registrationLink: yup
    .string()
    .nullable()
    .test('url', 'Please enter a valid URL', function(value) {
      if (!value || value.trim() === '') return true;
      return /^https?:\/\/.+/.test(value);
    }),

  address: yup
    .string()
    .nullable()
    .max(200, "Address must not exceed 200 characters"),

  state: yup.string().nullable(),

  city: yup.string().nullable(),

  zipcode: yup
    .string()
    .nullable()
    .test('zipcode', 'Zipcode must be 5-6 digits', function(value) {
      if (!value || value.trim() === '') return true;
      return /^[0-9]{5,6}$/.test(value);
    }),

  firstName: yup
    .string()
    .nullable()
    .test('firstName', 'First name must be at least 2 characters', function(value) {
      if (!value || value.trim() === '') return true;
      return value.length >= 2 && value.length <= 50;
    }),

  lastName: yup
    .string()
    .nullable()
    .test('lastName', 'Last name must be at least 2 characters', function(value) {
      if (!value || value.trim() === '') return true;
      return value.length >= 2 && value.length <= 50;
    }),

  contactPhone: yup
    .string()
    .nullable()
    .test('phone', 'Phone number must be 10 digits', function(value) {
      if (!value || value.trim() === '') return true;
      return /^[0-9]{10}$/.test(value.replace(/\D/g, ''));
    }),

  contactEmail: yup
    .string()
    .nullable()
    .test('email', 'Please enter a valid email address', function(value) {
      if (!value || value.trim() === '') return true;
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
    }),

  quickLinks: yup.array().of(yup.string()).nullable(),

  videos: yup.array().of(
    yup.object().shape({
      label: yup.string().required("Video label is required"),
      videoUrl: yup.string().url("Please enter a valid video URL").required("Video URL is required")
    })
  ).max(5, "Maximum 5 videos allowed").nullable(),

  isPublished: yup.boolean().required()
});

const AddAnnouncementPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const { showSuccess, showError } = useSnackbar();

  // Redux state
  const {
    annoucementType,
    orgAnnouncement: announcementCategories,
    loading,
  } = useSelector((state: RootState) => state.announcementDashboard);

  // Common slice state for states and locations
  const {
    allStatesList,
    allLocationsList,
  } = useSelector((state: RootState) => state.commonSlice);

  const { orgProfile, selectedState, selectedLocations, selectedSport, selectedSpecialities } = useSelector((state: RootState) => state.orgProfile);

  console.log("orgProfile", orgProfile[0]?.id, orgProfile[0]?.userId);

  // React Hook Form setup
  const {
    control,
    handleSubmit: onSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    reset,
    setError,
    clearErrors
  } = useForm<AnnouncementFormData>({
    // resolver: yupResolver(validationSchema),
    mode: 'onChange',
    defaultValues: {
      title: "",
      fullContent: "",
      image: null,
      dateRange: {
        start: null,
        end: null,
      },
      area: "",
      type: "",
      category: "",
      announcementFormat: "",
      registrationLink: "",
      address: "",
      state: "",
      city: "",
      zipcode: "",
      firstName: "",
      lastName: "",
      contactPhone: "",
      contactEmail: "",
      quickLinks: [""],
      videos: [],
      isPublished: false,
    }
  });

  // Watch form values for dependent fields
  const watchedState = watch("state");
  const watchedFullContent = watch("fullContent");
  const watchedDateRange = watch("dateRange");
  const watchedType = watch("type");
  const watchedCategory = watch("category");
  const watchedAnnouncementFormat = watch("announcementFormat");
  const watchedRegistrationLink = watch("registrationLink");
  const watchedAddress = watch("address");
  const watchedCity = watch("city");
  const watchedZipcode = watch("zipcode");
  const watchedFirstName = watch("firstName");
  const watchedLastName = watch("lastName");
  const watchedContactPhone = watch("contactPhone");
  const watchedContactEmail = watch("contactEmail");
  const watchedQuickLinks = watch("quickLinks");
  const watchedVideos = watch("videos");

  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Load dropdown data
  useEffect(() => {
    dispatch(fetchAnnouncementsCategory());
    dispatch(fetchAnnouncementType());
    dispatch(fetchAllStates());
  }, [dispatch]);

  // Fetch cities when state is selected
  useEffect(() => {
    if (watchedState) {
      const selectedStateId = parseInt(watchedState);
      if (selectedStateId) {
        dispatch(fetchAllLocations(selectedStateId));
      }
    }
  }, [watchedState, dispatch]);

  // Parse business info from slug
  const businessInfo = BusinessRoutes.parseSlug(params.slug as string);

  const handleInputChange = (field: keyof AnnouncementFormData, value: any) => {
    setValue(field, value);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg'];
      if (!allowedTypes.includes(file.type)) {
        showError("Please select a valid image file (PNG, JPG, JPEG).", "Invalid File Type");
        return;
      }

      // Validate file size (e.g., max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        showError("Image size should be less than 5MB.", "File Too Large");
        return;
      }

      // Convert to base64
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64String = e.target?.result as string;

        // Store base64 string in form data
        setValue('image', base64String);

        // Set preview
        setImagePreview(base64String);
      };
      reader.onerror = () => {
        showError("Error reading the image file.", "Upload Error");
      };
      reader.readAsDataURL(file);
    }
  };

  const handleQuickLinkChange = (index: number, value: string) => {
    const currentQuickLinks = watchedQuickLinks || [];
    const newQuickLinks = [...currentQuickLinks];
    newQuickLinks[index] = value;
    setValue('quickLinks', newQuickLinks);
  };

  const addQuickLink = () => {
    const currentQuickLinks = watchedQuickLinks || [];
    setValue('quickLinks', [...currentQuickLinks, ""]);
  };

  const removeQuickLink = (index: number) => {
    const currentQuickLinks = watchedQuickLinks || [];
    const newQuickLinks = currentQuickLinks.filter((_, i) => i !== index);
    setValue('quickLinks', newQuickLinks);
  };

  // Video management functions
  const [currentVideo, setCurrentVideo] = useState<VideoLink>({ label: "", videoUrl: "" });
  const [videoInputs, setVideoInputs] = useState<VideoLink[]>([{ label: "", videoUrl: "" }]);


  const handleVideoChange = (field: keyof VideoLink, value: string) => {
    setCurrentVideo(prev => ({ ...prev, [field]: value }));
  };

  const addVideo = () => {
    if (!currentVideo.label.trim() || !currentVideo.videoUrl.trim()) {
      showError("Please fill in both label and video URL.", "Validation Error");
      return;
    }

    //     if (formData.videos.length > 0) {
    //   const cleanVideos = formData.videos.filter(v => v.label && v.videoUrl);
    //   apiFormData.append('video', JSON.stringify(cleanVideos));
    // }

    const currentVideos = watchedVideos || [];
    if (currentVideos.length >= 5) {
      showError("You can add maximum 5 video links.", "Limit Reached");
      return;
    }

    // Basic URL validation
    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    if (!urlPattern.test(currentVideo.videoUrl)) {
      showError("Please enter a valid video URL.", "Invalid URL");
      return;
    }

    // setFormData(prev => ({
    //   ...prev,
    //   videos: [...prev.videos, { ...currentVideo }]
    // }));

    // Reset current video form and hide form
    setCurrentVideo({ label: "", videoUrl: "" });
    // setShowVideoForm(false);
    showSuccess("Video link added successfully!", "Success");
  };




  const handleFormSubmit = async (data: AnnouncementFormData) => {
    try {
      // Clear previous validation errors
      clearErrors();

      let hasErrors = false;

      // Manual validation with setError
      if (!data.title || data.title.trim().length < 3) {
        setError('title', {
          type: 'manual',
          message: 'Title is required and must be at least 3 characters'
        });
        hasErrors = true;
      }

      if (!data.fullContent || data.fullContent.trim().length < 10) {
        setError('fullContent', {
          type: 'manual',
          message: 'Content is required and must be at least 10 characters'
        });
        hasErrors = true;
      }

      if (!data.type) {
        setError('type', {
          type: 'manual',
          message: 'Type is required'
        });
        hasErrors = true;
      }

      if (!data.category) {
        setError('category', {
          type: 'manual',
          message: 'Category is required'
        });
        hasErrors = true;
      }

      if (!data.announcementFormat) {
        setError('announcementFormat', {
          type: 'manual',
          message: 'Announcement format is required'
        });
        hasErrors = true;
      }

      // Validate date range if provided
      if (data.dateRange?.start && data.dateRange?.end && data.dateRange.start > data.dateRange.end) {
        setError('dateRange', {
          type: 'manual',
          message: 'End date must be after start date'
        });
        hasErrors = true;
      }

      // Validate registration link if provided
      if (data.registrationLink && data.registrationLink.trim()) {
        const urlPattern = /^https?:\/\/.+/;
        if (!urlPattern.test(data.registrationLink)) {
          setError('registrationLink', {
            type: 'manual',
            message: 'Please enter a valid URL for registration link'
          });
          hasErrors = true;
        }
      }

      // Validate zipcode if provided
      if (data.zipcode && data.zipcode.trim()) {
        const zipcodePattern = /^[0-9]{5,6}$/;
        if (!zipcodePattern.test(data.zipcode)) {
          setError('zipcode', {
            type: 'manual',
            message: 'Zipcode must be 5-6 digits'
          });
          hasErrors = true;
        }
      }

      // Validate email if provided
      if (data.contactEmail && data.contactEmail.trim()) {
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(data.contactEmail)) {
          setError('contactEmail', {
            type: 'manual',
            message: 'Please enter a valid email address'
          });
          hasErrors = true;
        }
      }

      // Validate phone if provided
      if (data.contactPhone && data.contactPhone.trim()) {
        const phonePattern = /^[0-9]{10}$/;
        if (!phonePattern.test(data.contactPhone.replace(/\D/g, ''))) {
          setError('contactPhone', {
            type: 'manual',
            message: 'Phone number must be 10 digits'
          });
          hasErrors = true;
        }
      }

      // Validate first name if provided
      if (data.firstName && data.firstName.trim() && (data.firstName.trim().length < 2 || data.firstName.trim().length > 50)) {
        setError('firstName', {
          type: 'manual',
          message: 'First name must be between 2-50 characters'
        });
        hasErrors = true;
      }

      // Validate last name if provided
      if (data.lastName && data.lastName.trim() && (data.lastName.trim().length < 2 || data.lastName.trim().length > 50)) {
        setError('lastName', {
          type: 'manual',
          message: 'Last name must be between 2-50 characters'
        });
        hasErrors = true;
      }

      // Validate address if provided
      if (data.address && data.address.trim() && data.address.trim().length > 200) {
        setError('address', {
          type: 'manual',
          message: 'Address must not exceed 200 characters'
        });
        hasErrors = true;
      }

      // If there are validation errors, return early
      if (hasErrors) {
        showError("Please fix the validation errors before submitting", "Validation Error");
        return;
      }

      // Create payload object in the exact format required
      const payload: any = {
        announceTypeId: data.type ? parseInt(data.type) : null,
        announceCategoryId: data.category ? parseInt(data.category) : null,
        title: data.title,
        announcementFormat: data.announcementFormat,
        fullContent: data.fullContent,
        isPinned: data.isPublished,
        organizationId: 16,
        orgUserId: 444,
      };

      // Add organization ID from orgProfile
      if (orgProfile[0]?.id) {
        payload.organizationId = orgProfile[0].id;
      }

      // Add date range if exists
      if (data.dateRange?.start) {
        payload.startDate = data.dateRange.start.toISOString().split('T')[0];
      }
      if (data.dateRange?.end) {
        payload.endDate = data.dateRange.end.toISOString().split('T')[0];
      }

      // Add location data as nested object
      if (data.address || data.state || data.city || data.zipcode) {
        payload.location = {
          address: data.address || "",
          stateId: data.state ? parseInt(data.state) : null,
          cityId: data.city ? parseInt(data.city) : null,
          zipCode: data.zipcode || ""
        };
      }

      // Add contact data as nested object
      if (data.firstName || data.lastName || data.contactEmail || data.contactPhone) {
        payload.contact = {
          firstName: data.firstName || "",
          lastName: data.lastName || "",
          email: data.contactEmail || "",
          phone: data.contactPhone || ""
        };
      }

      // Add registration link
      if (data.registrationLink) {
        payload.registrationLink = data.registrationLink;
      }

      // Add base64 image if exists
      if (data.image) {
        payload.image = data.image;
      }

      // Process video inputs and add to payload
      const validVideos = videoInputs.filter(
        v => v.label.trim() && v.videoUrl.trim() && /^https?:\/\//.test(v.videoUrl.trim())
      );

      if (validVideos.length > 0) {
        payload.video = validVideos;
      }

      createAnnouncement

      // Convert payload to FormData for multipart submission
      const apiFormData = new FormData();

      // Add all payload fields to FormData
      Object.keys(payload).forEach(key => {
        if (payload[key] !== null && payload[key] !== undefined) {
          if (typeof payload[key] === 'object') {
            apiFormData.append(key, JSON.stringify(payload[key]));
          } else {
            apiFormData.append(key, payload[key].toString());
          }
        }
      });

      console.log("Submitting announcement data...");
      console.log("Payload structure:", JSON.stringify(payload, null, 2));
      console.log("FormData entries:", Array.from(apiFormData.entries()));

      // Call API
      const result = await dispatch(createAnnouncement(apiFormData));

      if (createAnnouncement.fulfilled.match(result)) {
        showSuccess("Announcement created successfully!", "Success");

        // Navigate back to announcements list
        const announcementsUrl = BusinessRoutes.announcements(businessInfo.businessName, businessInfo.userId);
        router.push(announcementsUrl);
      } else {
        showError("Failed to create announcement. Please try again.", "Error");
      }

    } catch (error) {
      console.error("Error creating announcement:", error);
      showError("Failed to create announcement. Please try again.", "Error");
    }
  };

  const handleCancel = () => {
    const announcementsUrl = BusinessRoutes.announcements(businessInfo.businessName, businessInfo.userId);
    router.push(announcementsUrl);
  };

  const handleBackToProfile = () => {
    const profileUrl = BusinessRoutes.profile(businessInfo.businessName, businessInfo.userId);
    router.push(profileUrl);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={handleBackToProfile}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Profile
              </Button>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-xl font-semibold text-gray-900">Add New Announcement</h1>
            </div>
            <Controller
              name="isPublished"
              control={control}
              render={({ field }) => (
                <div className="flex items-center gap-2">
                  <Label htmlFor="publish-toggle" className="text-sm font-medium">
                    Publish
                  </Label>
                  <Switch
                    id="publish-toggle"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </div>
              )}
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form onSubmit={onSubmit(handleFormSubmit)} className="space-y-8">
          {/* Title and Image Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Title and Details */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Announcement Title</CardTitle>
                </CardHeader>
                <CardContent>
                  <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Input
                          {...field}
                          placeholder="Enter announcement title"
                          className="text-lg"
                        />
                        {errors.title && (
                          <p className="text-red-500 text-sm mt-1">{errors.title.message}</p>
                        )}
                      </div>
                    )}
                  />
                </CardContent>
              </Card>





              <Card>
                <CardHeader>
                  <CardTitle>Announcement Details</CardTitle>
                  <p className="text-sm text-gray-600">(max 500 chars)</p>
                </CardHeader>
                <CardContent>
                  <Controller
                    name="fullContent"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Textarea
                          {...field}
                          placeholder="Enter announcement details..."
                          maxLength={500}
                          rows={6}
                          className="resize-none"
                        />
                        <div className="text-right text-sm text-gray-500 mt-2">
                          {watchedFullContent?.length || 0}/500
                        </div>
                        {errors.fullContent && (
                          <p className="text-red-500 text-sm mt-1">{errors.fullContent.message}</p>
                        )}
                      </div>
                    )}
                  />
                </CardContent>
              </Card>
            </div>

            {/* Image Upload */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle>Upload Image</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                    {imagePreview ? (
                      <div className="space-y-4">
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setImagePreview(null);
                            setValue('image', null);
                            // Reset the file input
                            const fileInput = document.getElementById('image-upload') as HTMLInputElement;
                            if (fileInput) fileInput.value = '';
                          }}
                        >
                          Remove Image
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                        <div>
                          <Label htmlFor="image-upload" className="cursor-pointer">
                            <span className="text-blue-600 hover:text-blue-500">Upload Image</span>
                            <span className="text-gray-500"> - png, jpg, jpeg</span>
                          </Label>
                          <Input
                            id="image-upload"
                            type="file"
                            accept="image/png,image/jpg,image/jpeg"
                            onChange={handleImageUpload}
                            className="hidden"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Date Selection Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Date Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <DateRangePicker
                    startDate={watchedDateRange?.start}
                    endDate={watchedDateRange?.end}
                    onDateChange={(dates: [Date | null, Date | null]) => {
                      handleInputChange('dateRange', {
                        start: dates[0],
                        end: dates[1]
                      });
                    }}
                  />
                  {errors.dateRange && (
                    <p className="text-red-500 text-sm mt-1">{errors.dateRange.message}</p>
                  )}
                </div>

                {/* <div className="space-y-2">
                  <Label htmlFor="inspection-date">Inspection Date</Label>
                  <Input
                    id="inspection-date"
                    type="date"
                    value={formData.inspectionDate}
                    onChange={(e) => handleInputChange('inspectionDate', e.target.value)}
                  />
                </div> */}

              </div>
            </CardContent>
          </Card>

          {/* Category and Type Section */}
          <Card>
            <CardHeader>
              <CardTitle>Classification</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="type">Type</Label>
                  <Select value={watchedType} onValueChange={(value) => handleInputChange('type', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Type" />
                    </SelectTrigger>
                    <SelectContent>
                      {annoucementType?.data?.map((type: any) => (
                        <SelectItem key={type.id} value={type.id.toString()}>
                          {type.typeName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.type && (
                    <p className="text-red-500 text-sm mt-1">{errors.type.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select value={watchedCategory} onValueChange={(value) => handleInputChange('category', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Category" />
                    </SelectTrigger>
                    <SelectContent>
                      {announcementCategories?.data?.map((category: any) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.categoryName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.category && (
                    <p className="text-red-500 text-sm mt-1">{errors.category.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="announcementFormat">Announcement Format</Label>
                  <Select value={watchedAnnouncementFormat} onValueChange={(value) => handleInputChange('announcementFormat', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Announcement Format" />
                    </SelectTrigger>
                    <SelectContent>
                      {channelsList?.map((channel: any) => (
                        <SelectItem key={channel.value} value={channel.value.toString()}>
                          {channel.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.announcementFormat && (
                    <p className="text-red-500 text-sm mt-1">{errors.announcementFormat.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="registrationLink">Registration Link</Label>
                  <Input
                    id="registrationLink"
                    placeholder="Enter Registration Link"
                    value={watchedRegistrationLink || ""}
                    onChange={(e) => handleInputChange('registrationLink', e.target.value)}
                  />
                  {errors.registrationLink && (
                    <p className="text-red-500 text-sm mt-1">{errors.registrationLink.message}</p>
                  )}
                </div>

              </div>
            </CardContent>
          </Card>

          {/* Location Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Location Info (if applicable)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <Label htmlFor="address">Location Address</Label>
                  <Textarea
                    id="address"
                    placeholder="Enter full address or location details"
                    rows={3}
                    className="mt-2"
                    value={watchedAddress || ""}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                  />
                  {errors.address && (
                    <p className="text-red-500 text-sm mt-1">{errors.address.message}</p>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="state">State</Label>
                    <Select value={watchedState} onValueChange={(value) => handleInputChange('state', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select State" />
                      </SelectTrigger>
                      <SelectContent>
                        {allStatesList?.map((state: any) => (
                          <SelectItem key={state.value} value={state.value.toString()}>
                            {state.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Select value={watchedCity} onValueChange={(value) => handleInputChange('city', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select City" />
                      </SelectTrigger>
                      <SelectContent>
                        {allLocationsList?.map((city: any) => (
                          <SelectItem key={city.value} value={city.value.toString()}>
                            {city.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>


                  <div className="space-y-2">
                    <Label htmlFor="zipcode">Zipcode</Label>
                    <Input
                      id="zipcode"
                      placeholder="Enter Zipcode here"
                      value={watchedZipcode || ""}
                      onChange={(e) => handleInputChange('zipcode', e.target.value)}
                    />
                    {errors.zipcode && (
                      <p className="text-red-500 text-sm mt-1">{errors.zipcode.message}</p>
                    )}
                  </div>


                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Details Section */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="first-name">First Name</Label>
                  <Input
                    id="first-name"
                    placeholder="Enter First name"
                    value={watchedFirstName || ""}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                  />
                  {errors.firstName && (
                    <p className="text-red-500 text-sm mt-1">{errors.firstName.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="last-name">Last Name</Label>
                  <Input
                    id="last-name"
                    placeholder="Contact Last name"
                    value={watchedLastName || ""}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                  />
                  {errors.lastName && (
                    <p className="text-red-500 text-sm mt-1">{errors.lastName.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact-phone">Mobile</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <Input
                      id="contact-phone"
                      placeholder="Phone number"
                      value={watchedContactPhone || ""}
                      onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  {errors.contactPhone && (
                    <p className="text-red-500 text-sm mt-1">{errors.contactPhone.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact-email">Email</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <Input
                      id="contact-email"
                      type="email"
                      placeholder="Email address"
                      value={watchedContactEmail || ""}
                      onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  {errors.contactEmail && (
                    <p className="text-red-500 text-sm mt-1">{errors.contactEmail.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Video Links Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Link className="w-5 h-5" />
                Video Links
              </CardTitle>
              <p className="text-sm text-gray-600">Add up to 5 video links for your announcement</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Add New Field Button */}
                <div className="text-right">
                  <Button
                    type="button"
                    onClick={() => {
                      if (videoInputs.length < 5) {
                        setVideoInputs(prev => [...prev, { label: "", videoUrl: "" }]);
                      }
                    }}
                    disabled={videoInputs.length >= 5}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Add Video Link ({videoInputs.length}/5)
                  </Button>
                </div>

                {/* Dynamic Fields */}
                {videoInputs.map((video, index) => (
                  <div key={index} className="grid grid-cols-1 md:grid-cols-2 gap-4 border border-gray-200 p-4 rounded-lg bg-gray-50 relative">
                    <div className="space-y-2">
                      <Label>Label</Label>
                      <Input
                        placeholder="e.g., Training Session"
                        value={video.label}
                        onChange={(e) => {
                          const updated = [...videoInputs];
                          updated[index].label = e.target.value;
                          setVideoInputs(updated);
                        }}
                        maxLength={50}
                      />
                      <div className="text-xs text-gray-500">{video.label.length}/50</div>
                    </div>
                    <div className="space-y-2">
                      <Label>Video URL</Label>
                      <Input
                        placeholder="https://youtube.com/..."
                        type="url"
                        value={video.videoUrl}
                        onChange={(e) => {
                          const updated = [...videoInputs];
                          updated[index].videoUrl = e.target.value;
                          setVideoInputs(updated);
                        }}
                      />
                    </div>
                    {videoInputs.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        className="absolute top-2 right-2 text-red-500"
                        onClick={() => {
                          const updated = videoInputs.filter((_, i) => i !== index);
                          setVideoInputs(updated);
                        }}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}

                <div className="text-xs text-gray-500 bg-blue-50 p-3 rounded-lg">
                  <strong>Tip:</strong> Add up to 5 video links (e.g., YouTube, Vimeo). Make sure they are public and accessible.
                </div>
              </div>
            </CardContent>

          </Card>

          {/* Action Buttons */}
          <div className="flex justify-center gap-4 pt-8">
            <Button
              type="button"
              variant="outline"
              onClick={handleBackToProfile}
              className="px-8"
            >
              Back To Profile
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="px-8 bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? "Creating..." : "Announcements"}
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleCancel}
              className="px-8"
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddAnnouncementPage;
